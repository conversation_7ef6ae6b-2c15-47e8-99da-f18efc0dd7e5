/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/_app"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/styles.css":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/styles.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  }\\n\\nbody {\\n  background: #1a1a1a;\\n  color: #fff;\\n  font-family: 'Segoe UI', Arial, sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  transform: scale(var(--interface-scale));\\n  transform-origin: top left;\\n  transition: transform 0.3s ease;\\n}\\n\\nheader {\\n  background: #2a2a2a;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  width: 360px;\\n  min-width: 320px;\\n  max-width: 400px;\\n  height: 800px;\\n  position: -webkit-sticky;\\n  position: sticky;\\n  top: 1rem;\\n  align-self: flex-start;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n#point-counter {\\n  font-size: 18px;\\n  margin-top: 20px;\\n  margin-bottom: 20px;\\n  align-self: flex-start;\\n  text-align: center;\\n  white-space: nowrap;\\n  min-height: 24px;\\n  color: #e0e0e0;\\n  font-weight: 500;\\n}\\n\\nmain {\\n  display: none;\\n  flex-direction: row;\\n  gap: 1.5rem;\\n  padding: 1.5rem;\\n  align-items: flex-start;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\nmain.visible {\\n  opacity: 1;\\n}\\n\\n.character-grid {\\n  display: grid;\\n  grid-template-columns: repeat(5, 1fr);\\n  grid-gap: 1.2rem;\\n  gap: 1.2rem;\\n  flex-grow: 1;\\n  padding: 0.5rem;\\n}\\n\\n.character {\\n  cursor: pointer;\\n  width: 100%;\\n  overflow: hidden;\\n  border-radius: 12px;\\n  border: 2px solid #3a3a3a;\\n  transition: all 0.3s ease;\\n  background: #2a2a2a;\\n  transform: scale(1.05);\\n}\\n\\n.character:hover {\\n  transform: translateY(-5px) scale(1.05);\\n  border-color: #4a4a4a;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.image-container {\\n  position: relative;\\n  width: 100%;\\n  padding-top: 133.33%;\\n  overflow: hidden;\\n  border-radius: 10px;\\n}\\n\\n.image-container img {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 10px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.character:hover .image-container img {\\n  transform: scale(1.05);\\n}\\n\\n.character.selected .image-container img {\\n  filter: brightness(25%);\\n}\\n\\n#chosenCharacterBox {\\n  position: relative;\\n  margin-top: 0px;\\n  border: 2px solid white;\\n}\\n\\n#chosenCharacterBox img {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.centered-menu {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100vh;\\n  text-align: center;\\n  position: relative;\\n  overflow: hidden;\\n  transition: opacity 0.3s ease;\\n  opacity: 1;\\n}\\n\\n.centered-menu.hidden {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n.psp-waves {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 110vw;\\n  height: 100vh;\\n  z-index: -1;\\n  overflow: hidden;\\n  pointer-events: none;\\n  opacity: 1;\\n  transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.wave {\\n  position: absolute;\\n  width: 200%;\\n  height: 70vh;\\n  bottom: 0;\\n  left: -50%;\\n  animation: waveMove 28s linear infinite;\\n  -webkit-mask-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' d='M0,192C120,192,240,192,360,197.3C480,203,600,213,720,208C840,203,960,181,1080,176C1200,171,1320,181,1440,181.3L1440,320L0,320Z'%3E%3C/path%3E%3C/svg%3E\\\");\\n          mask-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' d='M0,192C120,192,240,192,360,197.3C480,203,600,213,720,208C840,203,960,181,1080,176C1200,171,1320,181,1440,181.3L1440,320L0,320Z'%3E%3C/path%3E%3C/svg%3E\\\");\\n  -webkit-mask-size: cover;\\n          mask-size: cover;\\n  -webkit-mask-repeat: no-repeat;\\n          mask-repeat: no-repeat;\\n  -webkit-mask-position: center;\\n          mask-position: center;\\n  transform-origin: bottom center;\\n  will-change: transform;\\n  opacity: 1 !important;\\n}\\n\\n.wave:nth-child(1) {\\n  animation-duration: 28s;\\n  animation-delay: 0s;\\n  opacity: 1 !important;\\n}\\n\\n.wave:nth-child(2) {\\n  animation-duration: 22s;\\n  animation-delay: -5s;\\n  opacity: 0.8 !important;\\n}\\n\\n.wave:nth-child(3) {\\n  animation-duration: 16s;\\n  animation-delay: -10s;\\n  opacity: 0.6 !important;\\n}\\n\\n/* Ajustes para o tema escuro */\\nbody.dark .psp-waves {\\n  background: #1a1a1a;\\n}\\n\\nbody.dark .wave {\\n  background: linear-gradient(\\n    90deg,\\n    rgba(255,255,255,0) 0%,\\n    rgba(255,255,255,0.18) 20%,\\n    rgba(255,255,255,0.22) 50%,\\n    rgba(255,255,255,0.18) 80%,\\n    rgba(255,255,255,0) 100%\\n  );\\n  filter: brightness(1.2) blur(6px);\\n}\\n\\n/* Ajustes para o tema claro */\\nbody.light .psp-waves {\\n  background: #f5f5f5;\\n}\\n\\nbody.light .wave {\\n  background: linear-gradient(\\n    90deg,\\n    rgba(180,180,180,0) 0%,\\n    rgba(180,180,180,0.10) 20%,\\n    rgba(180,180,180,0.13) 50%,\\n    rgba(180,180,180,0.10) 80%,\\n    rgba(180,180,180,0) 100%\\n  );\\n  filter: blur(4px) brightness(1);\\n}\\n\\n/* Ajustes para o tema azul */\\nbody.blue .psp-waves {\\n  background: #1a1f2e;\\n}\\n\\nbody.blue .wave {\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(224, 231, 255, 0.1) 25%,\\n    rgba(224, 231, 255, 0.15) 50%,\\n    rgba(224, 231, 255, 0.1) 75%,\\n    transparent 100%\\n  );\\n}\\n\\n/* Ajustes para o tema verde */\\nbody.green .psp-waves {\\n  background: #1a2e1a;\\n}\\n\\nbody.green .wave {\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(224, 255, 224, 0.1) 25%,\\n    rgba(224, 255, 224, 0.15) 50%,\\n    rgba(224, 255, 224, 0.1) 76%,\\n    transparent 100%\\n  );\\n}\\n\\n/* Ajustes para o tema roxo */\\nbody.purple .psp-waves {\\n  background: #2e1a2e;\\n}\\n\\nbody.purple .wave {\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(255, 224, 255, 0.1) 25%,\\n    rgba(255, 224, 255, 0.15) 50%,\\n    rgba(255, 224, 255, 0.1) 75%,\\n    transparent 100%\\n  );\\n}\\n\\n@keyframes waveMove {\\n  0% {\\n    transform: translateX(-50%) scale(1);\\n    --wave-debug: 'Wave animation at 0%';\\n  }\\n  50% {\\n    transform: translateX(0%) scale(1.1);\\n    --wave-debug: 'Wave animation at 50%';\\n  }\\n  100% {\\n    transform: translateX(-50%) scale(1);\\n    --wave-debug: 'Wave animation at 100%';\\n  }\\n}\\n\\n/* Ajustes para o tema claro */\\nbody.light .centered-menu {\\n  background: #f5f5f5;\\n}\\n\\nbody.dark .centered-menu {\\n  background: #1a1a1a;\\n}\\n\\n.menu-button {\\n  background-color: #2a2a2a;\\n  color: #fff;\\n  border: 2px solid #3a3a3a;\\n  padding: 12px 24px;\\n  margin: 8px;\\n  border-radius: 12px;\\n  font-size: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  width: 200px;\\n  font-weight: 500;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.menu-button::after {\\n  content: '';\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 0;\\n  height: 0;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  transform: translate(-50%, -50%);\\n  transition: width 0.6s ease, height 0.6s ease;\\n}\\n\\n.menu-button:hover::after {\\n  width: 300px;\\n  height: 300px;\\n}\\n\\n.menu-button:active {\\n  transform: scale(0.95);\\n}\\n\\n.menu-button.small {\\n  width: auto;\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  margin-top: 10px;\\n}\\n\\n:root {\\n  --interface-scale: 1;\\n}\\n\\nbody.light {\\n  background-color: #f5f5f5;\\n  color: #333;\\n}\\n\\nbody.dark {\\n  background-color: #1a1a1a;\\n  color: #fff;\\n}\\n\\nbody.light header, \\nbody.light header * {\\n  color: #333 !important;\\n  background: transparent !important;\\n  opacity: 1 !important;\\n  visibility: visible !important;\\n  display: block !important;\\n  z-index: 100 !important;\\n}\\n\\nbody.dark header {\\n  background-color: #2a2a2a;\\n  color: #fff;\\n}\\n\\nbody.light .menu-button {\\n  background-color: #f7f7f7;\\n  color: #333;\\n  border: 2px solid #d0d0d0;\\n}\\n\\nbody.light .menu-button::after {\\n  background: rgba(0, 0, 0, 0.1);\\n}\\n\\nbody.dark .menu-button {\\n  background-color: #2a2a2a;\\n  color: #fff;\\n  border: 2px solid #3a3a3a;\\n}\\n\\nbody.light .menu-button:hover {\\n  background-color: #eaeaea;\\n  border-color: #bdbdbd;\\n}\\n\\nbody.dark .menu-button:hover::after {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\nbody.light .character {\\n  border: 2px solid #e0e0e0;\\n  background: #ffffff;\\n}\\n\\nbody.dark .character {\\n  border: 2px solid #3a3a3a;\\n  background: #2a2a2a;\\n}\\n\\nbody.light .character:hover {\\n  border-color: #d0d0d0;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\nbody.dark .character:hover {\\n  border-color: #4a4a4a;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.light #chosenCharacterBox {\\n  border: 2px solid #e0e0e0;\\n  background: #ffffff;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\\n  border: 2px solid gray;\\n  \\n}\\n\\nbody.dark #chosenCharacterBox {\\n  border: 2px solid #3a3a3a;\\n  background: #2a2a2a;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  border: 2px solid white;\\n}\\n\\nbody.light .dropdown-content {\\n  background-color: #ffffff;\\n  border: 2px solid #e0e0e0;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\nbody.dark .dropdown-content {\\n  background-color: #2a2a2a;\\n  border: 2px solid #3a3a3a;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.light .dropdown-item {\\n  color: #333;\\n}\\n\\nbody.dark .dropdown-item {\\n  color: #fff;\\n}\\n\\nbody.light .dropdown-item:hover {\\n  background-color: #f0f0f0;\\n}\\n\\nbody.dark .dropdown-item:hover {\\n  background-color: #3a3a3a;\\n  opacity: 1;\\n}\\n\\nbody.light #customizationMenu {\\n  display: none;\\n  opacity: 1;\\n  transition: opacity 0.3s ease;\\n}\\n\\nbody.dark #customizationMenu {\\n  background: #2a2a2a;\\n  border: 2px solid #3a3a3a;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.light #customizationMenu label {\\n  color: #333;\\n}\\n\\nbody.dark #customizationMenu label {\\n  color: #e0e0e0;\\n}\\n\\nbody.light #customizationMenu select {\\n  background: #f5f5f5;\\n  color: #333;\\n  border: 2px solid #e0e0e0;\\n}\\n\\nbody.dark #customizationMenu select {\\n  background: #3a3a3a;\\n  color: #fff;\\n  border: 2px solid #4a4a4a;\\n}\\n\\nbody.light #customizationMenu select:hover {\\n  background: #e8e8e8;\\n}\\n\\nbody.dark #customizationMenu select:hover {\\n  background: #4a4a4a;\\n}\\n\\nbody.light #scaleRange {\\n  background: #e0e0e0;\\n}\\n\\nbody.dark #scaleRange {\\n  background: #3a3a3a;\\n}\\n\\nbody.light .scale-value {\\n  color: #666;\\n}\\n\\nbody.dark .scale-value {\\n  color: #e0e0e0;\\n}\\n\\nbody.light #point-counter {\\n  color: #333;\\n}\\n\\nbody.dark #point-counter {\\n  color: #e0e0e0;\\n}\\n\\n.dropdown {\\n  position: relative;\\n  display: inline-block;\\n  width: 100%;\\n}\\n\\n.dropdown button {\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n.dropdown-content {\\n  display: none;\\n  position: absolute;\\n  background-color: #2a2a2a;\\n  min-width: 160px;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\\n  z-index: 1;\\n  border-radius: 12px;\\n  padding: 10px;\\n  width: 100%;\\n  margin-top: 5px;\\n  border: 2px solid #3a3a3a;\\n  max-height: 300px;\\n  overflow-y: scroll;\\n  overflow-x: hidden;\\n  position: relative;\\n}\\n\\n/* Remove scrollbar for Chrome, Safari and Opera */\\n.dropdown-content::-webkit-scrollbar {\\n  width: 0;\\n  background: transparent;\\n}\\n\\n/* Remove scrollbar for IE, Edge and Firefox */\\n.dropdown-content {\\n  -ms-overflow-style: none;\\n  scrollbar-width: none;\\n}\\n\\n.dropdown-content.show {\\n  display: block;\\n}\\n\\n/* Ajuste para o tema claro */\\nbody.light .dropdown-content {\\n  background-color: #ffffff;\\n  border: 2px solid #e0e0e0;\\n}\\n\\n/* Ajuste para o tema azul */\\nbody.blue .dropdown-content {\\n  background-color: #2a3447;\\n  border: 2px solid #3a4b6e;\\n}\\n\\n/* Ajuste para o tema verde */\\nbody.green .dropdown-content {\\n  background-color: #2a472a;\\n  border: 2px solid #3a6e3a;\\n}\\n\\n/* Ajuste para o tema roxo */\\nbody.purple .dropdown-content {\\n  background-color: #472a47;\\n  border: 2px solid #6e3a6e;\\n}\\n\\n.scroll-indicator {\\n  position: absolute;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 0;\\n  height: 0;\\n  border-left: 10px solid transparent;\\n  border-right: 10px solid transparent;\\n  opacity: 0.9;\\n  pointer-events: none;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.scroll-indicator.top {\\n  top: 0;\\n  border-bottom: 10px solid #666;\\n}\\n\\n.scroll-indicator.bottom {\\n  bottom: 0;\\n  border-top: 10px solid #666;\\n}\\n\\n/* Ajustes para os temas */\\nbody.dark .scroll-indicator.top {\\n  border-bottom-color: #666;\\n}\\n\\nbody.dark .scroll-indicator.bottom {\\n  border-top-color: #666;\\n}\\n\\nbody.light .scroll-indicator.top {\\n  border-bottom-color: #333;\\n}\\n\\nbody.light .scroll-indicator.bottom {\\n  border-top-color: #333;\\n}\\n\\nbody.blue .scroll-indicator.top {\\n  border-bottom-color: #4a5b8e;\\n}\\n\\nbody.blue .scroll-indicator.bottom {\\n  border-top-color: #4a5b8e;\\n}\\n\\nbody.green .scroll-indicator.top {\\n  border-bottom-color: #4a8e4a;\\n}\\n\\nbody.green .scroll-indicator.bottom {\\n  border-top-color: #4a8e4a;\\n}\\n\\nbody.purple .scroll-indicator.top {\\n  border-bottom-color: #8e4a8e;\\n}\\n\\nbody.purple .scroll-indicator.bottom {\\n  border-top-color: #8e4a8e;\\n}\\n\\n.dropdown-item {\\n  background-color: transparent;\\n  color: #fff;\\n  padding: 10px 16px;\\n  border: none;\\n  text-decoration: none;\\n  display: block;\\n  cursor: pointer;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  text-align: center;\\n  margin: 4px 0;\\n  font-weight: 500;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.dropdown-item:hover {\\n  background-color: #3a3a3a;\\n  transform: translateY(-2px);\\n}\\n\\n.controls {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  flex: 1 1;\\n  overflow-y: hidden;\\n  overflow-x: hidden;\\n  width: 100%;\\n}\\n\\n#customizationMenu {\\n  display: none;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 1rem;\\n  transition: opacity 0.3s ease;\\n  opacity: 0;\\n  background: #2a2a2a;\\n  padding: 2rem;\\n  border-radius: 15px;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\\n  border: 2px solid #3a3a3a;\\n  width: 300px;\\n}\\n\\n#customizationMenu.visible {\\n  opacity: 1;\\n}\\n\\n#customizationMenu label {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-size: 1.1rem;\\n  color: #e0e0e0;\\n  font-weight: 500;\\n}\\n\\n#customizationMenu select {\\n  width: 150px;\\n  padding: 10px;\\n  border-radius: 8px;\\n  background: #3a3a3a;\\n  color: white;\\n  border: 2px solid #4a4a4a;\\n  margin-bottom: 1rem;\\n  text-align: center;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n#customizationMenu select:hover {\\n  background: #4a4a4a;\\n}\\n\\n#customizationMenu select:focus {\\n  outline: none;\\n  border-color: #5a5a5a;\\n  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);\\n}\\n\\n#scaleRange {\\n  width: 200px;\\n  height: 8px;\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  background: #3a3a3a;\\n  outline: none;\\n  border-radius: 4px;\\n  margin: 10px auto;\\n  display: block;\\n}\\n\\n#scaleRange::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 20px;\\n  height: 20px;\\n  background: #fff;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  -webkit-transition: all 0.3s ease;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n\\n#scaleRange::-moz-range-thumb {\\n  width: 20px;\\n  height: 20px;\\n  background: #fff;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  -moz-transition: all 0.3s ease;\\n  transition: all 0.3s ease;\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n\\n#scaleRange::-webkit-slider-thumb:hover {\\n  background: #e0e0e0;\\n  transform: scale(1.1);\\n}\\n\\n#scaleRange::-moz-range-thumb:hover {\\n  background: #e0e0e0;\\n  transform: scale(1.1);\\n}\\n\\n.scale-value {\\n  text-align: center;\\n  margin-top: 8px;\\n  font-size: 0.9rem;\\n  color: #e0e0e0;\\n  font-weight: 500;\\n}\\n\\n/* Ajustes responsivos para diferentes escalas */\\n@media (max-width: 1400px) {\\n  main {\\n    padding: 1rem;\\n  }\\n  \\n  .character-grid {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n\\n@media (max-width: 1200px) {\\n  main {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  \\n  header {\\n    position: relative;\\n    top: 0;\\n    width: 100%;\\n    max-width: 600px;\\n    height: auto;\\n    margin-bottom: 1rem;\\n  }\\n  \\n  .character-grid {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .character-grid {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  \\n  #chosenDisplay {\\n    max-width: 250px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .character-grid {\\n    grid-template-columns: repeat(1, 1fr);\\n  }\\n  \\n  header {\\n    padding: 1rem;\\n  }\\n}\\n\\n#categoryButton {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background-color: #444;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  padding: 12px 24px;\\n  font-size: 16px;\\n  cursor: pointer;\\n  width: calc(100% - 2px);\\n  position: relative;\\n}\\n\\n.button-content {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.button-content span:first-child {\\n  flex: 1 1;\\n  text-align: center;\\n  white-space: nowrap;\\n  padding-right: 10px;\\n}\\n\\n.arrow {\\n  width: 0;\\n  height: 0;\\n  border-left: 6px solid transparent;\\n  border-right: 6px solid transparent;\\n  border-top: 6px solid white;\\n  transition: transform 0.3s ease;\\n  margin-left: 10px;\\n  flex-shrink: 0;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n#categoryButton:hover .arrow {\\n  transform: rotate(180deg);\\n}\\n\\n/* Ajustes para os temas */\\nbody.light #categoryButton {\\n  background-color: #e0e0e0;\\n  color: #333;\\n}\\n\\nbody.light .arrow {\\n  border-top-color: #333;\\n}\\n\\nbody.blue #categoryButton {\\n  background-color: #3a4b6e;\\n}\\n\\nbody.green #categoryButton {\\n  background-color: #3a6e3a;\\n}\\n\\nbody.purple #categoryButton {\\n  background-color: #6e3a6e;\\n}\\n\\n.character-grid p {\\n  text-align: center;\\n  margin: 20px 0;\\n  min-height: 24px;\\n  white-space: nowrap;\\n}\\n\\n/* Tema Azul */\\nbody.blue {\\n    background: #1a1f2e;\\n    color: #e0e7ff;\\n}\\n\\nbody.blue .header {\\n    background: #2a3447;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.blue .menu-button {\\n    background-color: #3a4b6e;\\n    color: #e0e7ff;\\n    border: 2px solid #4a5b8e;\\n}\\n\\nbody.blue .menu-button::after {\\n    background: rgba(224, 231, 255, 0.2);\\n}\\n\\nbody.blue .menu-button:hover {\\n    background: #4a5b8e;\\n}\\n\\nbody.blue .character {\\n    background: #2a3447;\\n    border: 1px solid #3a4b6e;\\n}\\n\\nbody.blue .character:hover {\\n    border-color: #4a5b8e;\\n    box-shadow: 0 4px 15px rgba(74, 91, 142, 0.3);\\n}\\n\\nbody.blue .customization-menu {\\n    background: #2a3447;\\n    border: 1px solid #3a4b6e;\\n}\\n\\nbody.blue select {\\n    background: #3a4b6e;\\n    color: #e0e7ff;\\n}\\n\\nbody.blue select:hover {\\n    background: #4a5b8e;\\n}\\n\\n/* Tema Verde */\\nbody.green {\\n    background: #1a2e1a;\\n    color: #e0ffe0;\\n}\\n\\nbody.green .header {\\n    background: #2a472a;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.green .menu-button {\\n    background-color: #3a6e3a;\\n    color: #e0ffe0;\\n    border: 2px solid #4a8e4a;\\n}\\n\\nbody.green .menu-button::after {\\n    background: rgba(224, 255, 224, 0.2);\\n}\\n\\nbody.green .menu-button:hover {\\n    background: #4a8e4a;\\n}\\n\\nbody.green .character {\\n    background: #2a472a;\\n    border: 1px solid #3a6e3a;\\n}\\n\\nbody.green .character:hover {\\n    border-color: #4a8e4a;\\n    box-shadow: 0 4px 15px rgba(74, 142, 74, 0.3);\\n}\\n\\nbody.green .customization-menu {\\n    background: #2a472a;\\n    border: 1px solid #3a6e3a;\\n}\\n\\nbody.green select {\\n    background: #3a6e3a;\\n    color: #e0ffe0;\\n}\\n\\nbody.green select:hover {\\n    background: #4a8e4a;\\n}\\n\\n/* Tema Roxo */\\nbody.purple {\\n    background: #2e1a2e;\\n    color: #ffe0ff;\\n}\\n\\nbody.purple .header {\\n    background: #472a47;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.purple .menu-button {\\n    background-color: #6e3a6e;\\n    color: #ffe0ff;\\n    border: 2px solid #8e4a8e;\\n}\\n\\nbody.purple .menu-button::after {\\n    background: rgba(255, 224, 255, 0.2);\\n}\\n\\nbody.purple .menu-button:hover {\\n    background: #8e4a8e;\\n}\\n\\nbody.purple .character {\\n    background: #472a47;\\n    border: 1px solid #6e3a6e;\\n}\\n\\nbody.purple .character:hover {\\n    border-color: #8e4a8e;\\n    box-shadow: 0 4px 15px rgba(142, 74, 142, 0.3);\\n}\\n\\nbody.purple .customization-menu {\\n    background: #472a47;\\n    border: 1px solid #6e3a6e;\\n}\\n\\nbody.purple select {\\n    background: #6e3a6e;\\n    color: #ffe0ff;\\n}\\n\\nbody.purple select:hover {\\n    background: #8e4a8e;\\n}\\n\\n/* Ajustes comuns para todos os temas */\\nbody.blue .point-counter,\\nbody.green .point-counter,\\nbody.purple .point-counter {\\n    color: #fff;\\n}\\n\\nbody.blue .character img,\\nbody.green .character img,\\nbody.purple .character img {\\n    border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\nbody.blue .character-name,\\nbody.green .character-name,\\nbody.purple .character-name {\\n    color: #fff;\\n}\\n\\nbody.blue .character-category,\\nbody.green .character-category,\\nbody.purple .character-category {\\n    color: rgba(255, 255, 255, 0.7);\\n}\\n\\n/* Ajustes do slider para os novos temas */\\nbody.blue #scaleRange::-webkit-slider-thumb {\\n    background: #4a5b8e;\\n}\\n\\nbody.green #scaleRange::-webkit-slider-thumb {\\n    background: #4a8e4a;\\n}\\n\\nbody.purple #scaleRange::-webkit-slider-thumb {\\n    background: #8e4a8e;\\n}\\n\\nbody.blue #scaleRange::-moz-range-thumb {\\n    background: #4a5b8e;\\n}\\n\\nbody.green #scaleRange::-moz-range-thumb {\\n    background: #4a8e4a;\\n}\\n\\nbody.purple #scaleRange::-moz-range-thumb {\\n    background: #8e4a8e;\\n}\\n\\n/* Ajuste responsivo para telas menores */\\n@media (max-width: 1200px) {\\n  .character-grid {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n\\n#chosenDisplay {\\n  margin-top: 0.5rem;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.3rem;\\n  transition: opacity 0.3s ease;\\n  opacity: 1;\\n  max-width: 180px;\\n  margin: 0 auto;\\n  position:-webkit-sticky;\\n  position:sticky;\\n}\\n\\n#chosenDisplay.hidden {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n#startMenu {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 1rem;\\n  transition: opacity 0.5s ease;\\n  opacity: 1;\\n}\\n\\n#startMenu.hidden {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n#startMenu.centered-menu {\\n  background: transparent !important;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/styles.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;EACtB;;AAEF;EACE,mBAAmB;EACnB,WAAW;EACX,0CAA0C;EAC1C,SAAS;EACT,UAAU;EACV,wCAAwC;EACxC,0BAA0B;EAC1B,+BAA+B;AACjC;;AAEA;EACE,mBAAmB;EACnB,mBAAmB;EACnB,eAAe;EACf,YAAY;EACZ,gBAAgB;EAChB,gBAAgB;EAChB,aAAa;EACb,wBAAgB;EAAhB,gBAAgB;EAChB,SAAS;EACT,sBAAsB;EACtB,aAAa;EACb,sBAAsB;EACtB,gBAAgB;EAChB,kBAAkB;EAClB,wCAAwC;AAC1C;;AAEA;EACE,eAAe;EACf,gBAAgB;EAChB,mBAAmB;EACnB,sBAAsB;EACtB,kBAAkB;EAClB,mBAAmB;EACnB,gBAAgB;EAChB,cAAc;EACd,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,eAAe;EACf,uBAAuB;EACvB,iBAAiB;EACjB,cAAc;EACd,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,qCAAqC;EACrC,gBAAW;EAAX,WAAW;EACX,YAAY;EACZ,eAAe;AACjB;;AAEA;EACE,eAAe;EACf,WAAW;EACX,gBAAgB;EAChB,mBAAmB;EACnB,yBAAyB;EACzB,yBAAyB;EACzB,mBAAmB;EACnB,sBAAsB;AACxB;;AAEA;EACE,uCAAuC;EACvC,qBAAqB;EACrB,yCAAyC;AAC3C;;AAEA;EACE,kBAAkB;EAClB,WAAW;EACX,oBAAoB;EACpB,gBAAgB;EAChB,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,iBAAiB;EACjB,mBAAmB;EACnB,+BAA+B;AACjC;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,kBAAkB;EAClB,eAAe;EACf,uBAAuB;AACzB;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,iBAAiB;AACnB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,aAAa;EACb,kBAAkB;EAClB,kBAAkB;EAClB,gBAAgB;EAChB,6BAA6B;EAC7B,UAAU;AACZ;;AAEA;EACE,UAAU;EACV,oBAAoB;AACtB;;AAEA;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,YAAY;EACZ,aAAa;EACb,WAAW;EACX,gBAAgB;EAChB,oBAAoB;EACpB,UAAU;EACV,qDAAqD;AACvD;;AAEA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,SAAS;EACT,UAAU;EACV,uCAAuC;EACvC,oSAA4R;UAA5R,4RAA4R;EAC5R,wBAAgB;UAAhB,gBAAgB;EAChB,8BAAsB;UAAtB,sBAAsB;EACtB,6BAAqB;UAArB,qBAAqB;EACrB,+BAA+B;EAC/B,sBAAsB;EACtB,qBAAqB;AACvB;;AAEA;EACE,uBAAuB;EACvB,mBAAmB;EACnB,qBAAqB;AACvB;;AAEA;EACE,uBAAuB;EACvB,oBAAoB;EACpB,uBAAuB;AACzB;;AAEA;EACE,uBAAuB;EACvB,qBAAqB;EACrB,uBAAuB;AACzB;;AAEA,+BAA+B;AAC/B;EACE,mBAAmB;AACrB;;AAEA;EACE;;;;;;;GAOC;EACD,iCAAiC;AACnC;;AAEA,8BAA8B;AAC9B;EACE,mBAAmB;AACrB;;AAEA;EACE;;;;;;;GAOC;EACD,+BAA+B;AACjC;;AAEA,6BAA6B;AAC7B;EACE,mBAAmB;AACrB;;AAEA;EACE;;;;;;;GAOC;AACH;;AAEA,8BAA8B;AAC9B;EACE,mBAAmB;AACrB;;AAEA;EACE;;;;;;;GAOC;AACH;;AAEA,6BAA6B;AAC7B;EACE,mBAAmB;AACrB;;AAEA;EACE;;;;;;;GAOC;AACH;;AAEA;EACE;IACE,oCAAoC;IACpC,oCAAoC;EACtC;EACA;IACE,oCAAoC;IACpC,qCAAqC;EACvC;EACA;IACE,oCAAoC;IACpC,sCAAsC;EACxC;AACF;;AAEA,8BAA8B;AAC9B;EACE,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,yBAAyB;EACzB,WAAW;EACX,yBAAyB;EACzB,kBAAkB;EAClB,WAAW;EACX,mBAAmB;EACnB,eAAe;EACf,eAAe;EACf,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,QAAQ;EACR,SAAS;EACT,oCAAoC;EACpC,kBAAkB;EAClB,gCAAgC;EAChC,6CAA6C;AAC/C;;AAEA;EACE,YAAY;EACZ,aAAa;AACf;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,WAAW;EACX,iBAAiB;EACjB,eAAe;EACf,gBAAgB;AAClB;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,yBAAyB;EACzB,WAAW;AACb;;AAEA;EACE,yBAAyB;EACzB,WAAW;AACb;;AAEA;;EAEE,sBAAsB;EACtB,kCAAkC;EAClC,qBAAqB;EACrB,8BAA8B;EAC9B,yBAAyB;EACzB,uBAAuB;AACzB;;AAEA;EACE,yBAAyB;EACzB,WAAW;AACb;;AAEA;EACE,yBAAyB;EACzB,WAAW;EACX,yBAAyB;AAC3B;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,yBAAyB;EACzB,WAAW;EACX,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;AACvB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,yBAAyB;EACzB,mBAAmB;AACrB;;AAEA;EACE,yBAAyB;EACzB,mBAAmB;AACrB;;AAEA;EACE,qBAAqB;EACrB,yCAAyC;AAC3C;;AAEA;EACE,qBAAqB;EACrB,yCAAyC;AAC3C;;AAEA;EACE,yBAAyB;EACzB,mBAAmB;EACnB,yCAAyC;EACzC,sBAAsB;;AAExB;;AAEA;EACE,yBAAyB;EACzB,mBAAmB;EACnB,wCAAwC;EACxC,uBAAuB;AACzB;;AAEA;EACE,yBAAyB;EACzB,yBAAyB;EACzB,yCAAyC;AAC3C;;AAEA;EACE,yBAAyB;EACzB,yBAAyB;EACzB,yCAAyC;AAC3C;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,mBAAmB;EACnB,yBAAyB;EACzB,yCAAyC;AAC3C;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,mBAAmB;EACnB,WAAW;EACX,yBAAyB;AAC3B;;AAEA;EACE,mBAAmB;EACnB,WAAW;EACX,yBAAyB;AAC3B;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;AACb;;AAEA;EACE,WAAW;EACX,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,kBAAkB;EAClB,yBAAyB;EACzB,gBAAgB;EAChB,yCAAyC;EACzC,UAAU;EACV,mBAAmB;EACnB,aAAa;EACb,WAAW;EACX,eAAe;EACf,yBAAyB;EACzB,iBAAiB;EACjB,kBAAkB;EAClB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA,kDAAkD;AAClD;EACE,QAAQ;EACR,uBAAuB;AACzB;;AAEA,8CAA8C;AAC9C;EACE,wBAAwB;EACxB,qBAAqB;AACvB;;AAEA;EACE,cAAc;AAChB;;AAEA,6BAA6B;AAC7B;EACE,yBAAyB;EACzB,yBAAyB;AAC3B;;AAEA,4BAA4B;AAC5B;EACE,yBAAyB;EACzB,yBAAyB;AAC3B;;AAEA,6BAA6B;AAC7B;EACE,yBAAyB;EACzB,yBAAyB;AAC3B;;AAEA,4BAA4B;AAC5B;EACE,yBAAyB;EACzB,yBAAyB;AAC3B;;AAEA;EACE,kBAAkB;EAClB,SAAS;EACT,2BAA2B;EAC3B,QAAQ;EACR,SAAS;EACT,mCAAmC;EACnC,oCAAoC;EACpC,YAAY;EACZ,oBAAoB;EACpB,6BAA6B;AAC/B;;AAEA;EACE,MAAM;EACN,8BAA8B;AAChC;;AAEA;EACE,SAAS;EACT,2BAA2B;AAC7B;;AAEA,0BAA0B;AAC1B;EACE,yBAAyB;AAC3B;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,6BAA6B;EAC7B,WAAW;EACX,kBAAkB;EAClB,YAAY;EACZ,qBAAqB;EACrB,cAAc;EACd,eAAe;EACf,kBAAkB;EAClB,yBAAyB;EACzB,WAAW;EACX,kBAAkB;EAClB,aAAa;EACb,gBAAgB;EAChB,mBAAmB;EACnB,gBAAgB;EAChB,uBAAuB;AACzB;;AAEA;EACE,yBAAyB;EACzB,2BAA2B;AAC7B;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,SAAS;EACT,SAAO;EACP,kBAAkB;EAClB,kBAAkB;EAClB,WAAW;AACb;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,SAAS;EACT,6BAA6B;EAC7B,UAAU;EACV,mBAAmB;EACnB,aAAa;EACb,mBAAmB;EACnB,yCAAyC;EACzC,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,cAAc;EACd,qBAAqB;EACrB,iBAAiB;EACjB,cAAc;EACd,gBAAgB;AAClB;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,kBAAkB;EAClB,mBAAmB;EACnB,YAAY;EACZ,yBAAyB;EACzB,mBAAmB;EACnB,kBAAkB;EAClB,gBAAgB;EAChB,eAAe;EACf,yBAAyB;AAC3B;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,aAAa;EACb,qBAAqB;EACrB,8CAA8C;AAChD;;AAEA;EACE,YAAY;EACZ,WAAW;EACX,wBAAwB;EACxB,qBAAgB;OAAhB,gBAAgB;EAChB,mBAAmB;EACnB,aAAa;EACb,kBAAkB;EAClB,iBAAiB;EACjB,cAAc;AAChB;;AAEA;EACE,wBAAwB;EACxB,gBAAgB;EAChB,WAAW;EACX,YAAY;EACZ,gBAAgB;EAChB,kBAAkB;EAClB,eAAe;EACf,iCAAyB;EAAzB,yBAAyB;EACzB,wCAAwC;AAC1C;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,gBAAgB;EAChB,kBAAkB;EAClB,eAAe;EACf,8BAAyB;EAAzB,yBAAyB;EACzB,YAAY;EACZ,wCAAwC;AAC1C;;AAEA;EACE,mBAAmB;EACnB,qBAAqB;AACvB;;AAEA;EACE,mBAAmB;EACnB,qBAAqB;AACvB;;AAEA;EACE,kBAAkB;EAClB,eAAe;EACf,iBAAiB;EACjB,cAAc;EACd,gBAAgB;AAClB;;AAEA,gDAAgD;AAChD;EACE;IACE,aAAa;EACf;;EAEA;IACE,qCAAqC;EACvC;AACF;;AAEA;EACE;IACE,sBAAsB;IACtB,mBAAmB;EACrB;;EAEA;IACE,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,gBAAgB;IAChB,YAAY;IACZ,mBAAmB;EACrB;;EAEA;IACE,qCAAqC;EACvC;AACF;;AAEA;EACE;IACE,qCAAqC;EACvC;;EAEA;IACE,gBAAgB;EAClB;AACF;;AAEA;EACE;IACE,qCAAqC;EACvC;;EAEA;IACE,aAAa;EACf;AACF;;AAEA;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,sBAAsB;EACtB,YAAY;EACZ,YAAY;EACZ,kBAAkB;EAClB,kBAAkB;EAClB,eAAe;EACf,eAAe;EACf,uBAAuB;EACvB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,WAAW;EACX,YAAY;AACd;;AAEA;EACE,SAAO;EACP,kBAAkB;EAClB,mBAAmB;EACnB,mBAAmB;AACrB;;AAEA;EACE,QAAQ;EACR,SAAS;EACT,kCAAkC;EAClC,mCAAmC;EACnC,2BAA2B;EAC3B,+BAA+B;EAC/B,iBAAiB;EACjB,cAAc;EACd,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,yBAAyB;AAC3B;;AAEA,0BAA0B;AAC1B;EACE,yBAAyB;EACzB,WAAW;AACb;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,kBAAkB;EAClB,cAAc;EACd,gBAAgB;EAChB,mBAAmB;AACrB;;AAEA,cAAc;AACd;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,yCAAyC;AAC7C;;AAEA;IACI,yBAAyB;IACzB,cAAc;IACd,yBAAyB;AAC7B;;AAEA;IACI,oCAAoC;AACxC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;AAC7B;;AAEA;IACI,qBAAqB;IACrB,6CAA6C;AACjD;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;AAC7B;;AAEA;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,eAAe;AACf;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,yCAAyC;AAC7C;;AAEA;IACI,yBAAyB;IACzB,cAAc;IACd,yBAAyB;AAC7B;;AAEA;IACI,oCAAoC;AACxC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;AAC7B;;AAEA;IACI,qBAAqB;IACrB,6CAA6C;AACjD;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;AAC7B;;AAEA;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,cAAc;AACd;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,mBAAmB;IACnB,yCAAyC;AAC7C;;AAEA;IACI,yBAAyB;IACzB,cAAc;IACd,yBAAyB;AAC7B;;AAEA;IACI,oCAAoC;AACxC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;AAC7B;;AAEA;IACI,qBAAqB;IACrB,8CAA8C;AAClD;;AAEA;IACI,mBAAmB;IACnB,yBAAyB;AAC7B;;AAEA;IACI,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,uCAAuC;AACvC;;;IAGI,WAAW;AACf;;AAEA;;;IAGI,0CAA0C;AAC9C;;AAEA;;;IAGI,WAAW;AACf;;AAEA;;;IAGI,+BAA+B;AACnC;;AAEA,0CAA0C;AAC1C;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,yCAAyC;AACzC;EACE;IACE,qCAAqC;EACvC;AACF;;AAEA;EACE,kBAAkB;EAClB,mBAAmB;EACnB,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,WAAW;EACX,6BAA6B;EAC7B,UAAU;EACV,gBAAgB;EAChB,cAAc;EACd,uBAAe;EAAf,eAAe;AACjB;;AAEA;EACE,UAAU;EACV,oBAAoB;AACtB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,SAAS;EACT,6BAA6B;EAC7B,UAAU;AACZ;;AAEA;EACE,UAAU;EACV,oBAAoB;AACtB;;AAEA;EACE,kCAAkC;AACpC\",\"sourcesContent\":[\"* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  }\\n\\nbody {\\n  background: #1a1a1a;\\n  color: #fff;\\n  font-family: 'Segoe UI', Arial, sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  transform: scale(var(--interface-scale));\\n  transform-origin: top left;\\n  transition: transform 0.3s ease;\\n}\\n\\nheader {\\n  background: #2a2a2a;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  width: 360px;\\n  min-width: 320px;\\n  max-width: 400px;\\n  height: 800px;\\n  position: sticky;\\n  top: 1rem;\\n  align-self: flex-start;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n#point-counter {\\n  font-size: 18px;\\n  margin-top: 20px;\\n  margin-bottom: 20px;\\n  align-self: flex-start;\\n  text-align: center;\\n  white-space: nowrap;\\n  min-height: 24px;\\n  color: #e0e0e0;\\n  font-weight: 500;\\n}\\n\\nmain {\\n  display: none;\\n  flex-direction: row;\\n  gap: 1.5rem;\\n  padding: 1.5rem;\\n  align-items: flex-start;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\nmain.visible {\\n  opacity: 1;\\n}\\n\\n.character-grid {\\n  display: grid;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 1.2rem;\\n  flex-grow: 1;\\n  padding: 0.5rem;\\n}\\n\\n.character {\\n  cursor: pointer;\\n  width: 100%;\\n  overflow: hidden;\\n  border-radius: 12px;\\n  border: 2px solid #3a3a3a;\\n  transition: all 0.3s ease;\\n  background: #2a2a2a;\\n  transform: scale(1.05);\\n}\\n\\n.character:hover {\\n  transform: translateY(-5px) scale(1.05);\\n  border-color: #4a4a4a;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.image-container {\\n  position: relative;\\n  width: 100%;\\n  padding-top: 133.33%;\\n  overflow: hidden;\\n  border-radius: 10px;\\n}\\n\\n.image-container img {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 10px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.character:hover .image-container img {\\n  transform: scale(1.05);\\n}\\n\\n.character.selected .image-container img {\\n  filter: brightness(25%);\\n}\\n\\n#chosenCharacterBox {\\n  position: relative;\\n  margin-top: 0px;\\n  border: 2px solid white;\\n}\\n\\n#chosenCharacterBox img {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.centered-menu {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100vh;\\n  text-align: center;\\n  position: relative;\\n  overflow: hidden;\\n  transition: opacity 0.3s ease;\\n  opacity: 1;\\n}\\n\\n.centered-menu.hidden {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n.psp-waves {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 110vw;\\n  height: 100vh;\\n  z-index: -1;\\n  overflow: hidden;\\n  pointer-events: none;\\n  opacity: 1;\\n  transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.wave {\\n  position: absolute;\\n  width: 200%;\\n  height: 70vh;\\n  bottom: 0;\\n  left: -50%;\\n  animation: waveMove 28s linear infinite;\\n  mask-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' d='M0,192C120,192,240,192,360,197.3C480,203,600,213,720,208C840,203,960,181,1080,176C1200,171,1320,181,1440,181.3L1440,320L0,320Z'%3E%3C/path%3E%3C/svg%3E\\\");\\n  mask-size: cover;\\n  mask-repeat: no-repeat;\\n  mask-position: center;\\n  transform-origin: bottom center;\\n  will-change: transform;\\n  opacity: 1 !important;\\n}\\n\\n.wave:nth-child(1) {\\n  animation-duration: 28s;\\n  animation-delay: 0s;\\n  opacity: 1 !important;\\n}\\n\\n.wave:nth-child(2) {\\n  animation-duration: 22s;\\n  animation-delay: -5s;\\n  opacity: 0.8 !important;\\n}\\n\\n.wave:nth-child(3) {\\n  animation-duration: 16s;\\n  animation-delay: -10s;\\n  opacity: 0.6 !important;\\n}\\n\\n/* Ajustes para o tema escuro */\\nbody.dark .psp-waves {\\n  background: #1a1a1a;\\n}\\n\\nbody.dark .wave {\\n  background: linear-gradient(\\n    90deg,\\n    rgba(255,255,255,0) 0%,\\n    rgba(255,255,255,0.18) 20%,\\n    rgba(255,255,255,0.22) 50%,\\n    rgba(255,255,255,0.18) 80%,\\n    rgba(255,255,255,0) 100%\\n  );\\n  filter: brightness(1.2) blur(6px);\\n}\\n\\n/* Ajustes para o tema claro */\\nbody.light .psp-waves {\\n  background: #f5f5f5;\\n}\\n\\nbody.light .wave {\\n  background: linear-gradient(\\n    90deg,\\n    rgba(180,180,180,0) 0%,\\n    rgba(180,180,180,0.10) 20%,\\n    rgba(180,180,180,0.13) 50%,\\n    rgba(180,180,180,0.10) 80%,\\n    rgba(180,180,180,0) 100%\\n  );\\n  filter: blur(4px) brightness(1);\\n}\\n\\n/* Ajustes para o tema azul */\\nbody.blue .psp-waves {\\n  background: #1a1f2e;\\n}\\n\\nbody.blue .wave {\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(224, 231, 255, 0.1) 25%,\\n    rgba(224, 231, 255, 0.15) 50%,\\n    rgba(224, 231, 255, 0.1) 75%,\\n    transparent 100%\\n  );\\n}\\n\\n/* Ajustes para o tema verde */\\nbody.green .psp-waves {\\n  background: #1a2e1a;\\n}\\n\\nbody.green .wave {\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(224, 255, 224, 0.1) 25%,\\n    rgba(224, 255, 224, 0.15) 50%,\\n    rgba(224, 255, 224, 0.1) 76%,\\n    transparent 100%\\n  );\\n}\\n\\n/* Ajustes para o tema roxo */\\nbody.purple .psp-waves {\\n  background: #2e1a2e;\\n}\\n\\nbody.purple .wave {\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(255, 224, 255, 0.1) 25%,\\n    rgba(255, 224, 255, 0.15) 50%,\\n    rgba(255, 224, 255, 0.1) 75%,\\n    transparent 100%\\n  );\\n}\\n\\n@keyframes waveMove {\\n  0% {\\n    transform: translateX(-50%) scale(1);\\n    --wave-debug: 'Wave animation at 0%';\\n  }\\n  50% {\\n    transform: translateX(0%) scale(1.1);\\n    --wave-debug: 'Wave animation at 50%';\\n  }\\n  100% {\\n    transform: translateX(-50%) scale(1);\\n    --wave-debug: 'Wave animation at 100%';\\n  }\\n}\\n\\n/* Ajustes para o tema claro */\\nbody.light .centered-menu {\\n  background: #f5f5f5;\\n}\\n\\nbody.dark .centered-menu {\\n  background: #1a1a1a;\\n}\\n\\n.menu-button {\\n  background-color: #2a2a2a;\\n  color: #fff;\\n  border: 2px solid #3a3a3a;\\n  padding: 12px 24px;\\n  margin: 8px;\\n  border-radius: 12px;\\n  font-size: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  width: 200px;\\n  font-weight: 500;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.menu-button::after {\\n  content: '';\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 0;\\n  height: 0;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  transform: translate(-50%, -50%);\\n  transition: width 0.6s ease, height 0.6s ease;\\n}\\n\\n.menu-button:hover::after {\\n  width: 300px;\\n  height: 300px;\\n}\\n\\n.menu-button:active {\\n  transform: scale(0.95);\\n}\\n\\n.menu-button.small {\\n  width: auto;\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  margin-top: 10px;\\n}\\n\\n:root {\\n  --interface-scale: 1;\\n}\\n\\nbody.light {\\n  background-color: #f5f5f5;\\n  color: #333;\\n}\\n\\nbody.dark {\\n  background-color: #1a1a1a;\\n  color: #fff;\\n}\\n\\nbody.light header, \\nbody.light header * {\\n  color: #333 !important;\\n  background: transparent !important;\\n  opacity: 1 !important;\\n  visibility: visible !important;\\n  display: block !important;\\n  z-index: 100 !important;\\n}\\n\\nbody.dark header {\\n  background-color: #2a2a2a;\\n  color: #fff;\\n}\\n\\nbody.light .menu-button {\\n  background-color: #f7f7f7;\\n  color: #333;\\n  border: 2px solid #d0d0d0;\\n}\\n\\nbody.light .menu-button::after {\\n  background: rgba(0, 0, 0, 0.1);\\n}\\n\\nbody.dark .menu-button {\\n  background-color: #2a2a2a;\\n  color: #fff;\\n  border: 2px solid #3a3a3a;\\n}\\n\\nbody.light .menu-button:hover {\\n  background-color: #eaeaea;\\n  border-color: #bdbdbd;\\n}\\n\\nbody.dark .menu-button:hover::after {\\n  background: rgba(255, 255, 255, 0.2);\\n}\\n\\nbody.light .character {\\n  border: 2px solid #e0e0e0;\\n  background: #ffffff;\\n}\\n\\nbody.dark .character {\\n  border: 2px solid #3a3a3a;\\n  background: #2a2a2a;\\n}\\n\\nbody.light .character:hover {\\n  border-color: #d0d0d0;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\nbody.dark .character:hover {\\n  border-color: #4a4a4a;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.light #chosenCharacterBox {\\n  border: 2px solid #e0e0e0;\\n  background: #ffffff;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\\n  border: 2px solid gray;\\n  \\n}\\n\\nbody.dark #chosenCharacterBox {\\n  border: 2px solid #3a3a3a;\\n  background: #2a2a2a;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  border: 2px solid white;\\n}\\n\\nbody.light .dropdown-content {\\n  background-color: #ffffff;\\n  border: 2px solid #e0e0e0;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\nbody.dark .dropdown-content {\\n  background-color: #2a2a2a;\\n  border: 2px solid #3a3a3a;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.light .dropdown-item {\\n  color: #333;\\n}\\n\\nbody.dark .dropdown-item {\\n  color: #fff;\\n}\\n\\nbody.light .dropdown-item:hover {\\n  background-color: #f0f0f0;\\n}\\n\\nbody.dark .dropdown-item:hover {\\n  background-color: #3a3a3a;\\n  opacity: 1;\\n}\\n\\nbody.light #customizationMenu {\\n  display: none;\\n  opacity: 1;\\n  transition: opacity 0.3s ease;\\n}\\n\\nbody.dark #customizationMenu {\\n  background: #2a2a2a;\\n  border: 2px solid #3a3a3a;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.light #customizationMenu label {\\n  color: #333;\\n}\\n\\nbody.dark #customizationMenu label {\\n  color: #e0e0e0;\\n}\\n\\nbody.light #customizationMenu select {\\n  background: #f5f5f5;\\n  color: #333;\\n  border: 2px solid #e0e0e0;\\n}\\n\\nbody.dark #customizationMenu select {\\n  background: #3a3a3a;\\n  color: #fff;\\n  border: 2px solid #4a4a4a;\\n}\\n\\nbody.light #customizationMenu select:hover {\\n  background: #e8e8e8;\\n}\\n\\nbody.dark #customizationMenu select:hover {\\n  background: #4a4a4a;\\n}\\n\\nbody.light #scaleRange {\\n  background: #e0e0e0;\\n}\\n\\nbody.dark #scaleRange {\\n  background: #3a3a3a;\\n}\\n\\nbody.light .scale-value {\\n  color: #666;\\n}\\n\\nbody.dark .scale-value {\\n  color: #e0e0e0;\\n}\\n\\nbody.light #point-counter {\\n  color: #333;\\n}\\n\\nbody.dark #point-counter {\\n  color: #e0e0e0;\\n}\\n\\n.dropdown {\\n  position: relative;\\n  display: inline-block;\\n  width: 100%;\\n}\\n\\n.dropdown button {\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n.dropdown-content {\\n  display: none;\\n  position: absolute;\\n  background-color: #2a2a2a;\\n  min-width: 160px;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\\n  z-index: 1;\\n  border-radius: 12px;\\n  padding: 10px;\\n  width: 100%;\\n  margin-top: 5px;\\n  border: 2px solid #3a3a3a;\\n  max-height: 300px;\\n  overflow-y: scroll;\\n  overflow-x: hidden;\\n  position: relative;\\n}\\n\\n/* Remove scrollbar for Chrome, Safari and Opera */\\n.dropdown-content::-webkit-scrollbar {\\n  width: 0;\\n  background: transparent;\\n}\\n\\n/* Remove scrollbar for IE, Edge and Firefox */\\n.dropdown-content {\\n  -ms-overflow-style: none;\\n  scrollbar-width: none;\\n}\\n\\n.dropdown-content.show {\\n  display: block;\\n}\\n\\n/* Ajuste para o tema claro */\\nbody.light .dropdown-content {\\n  background-color: #ffffff;\\n  border: 2px solid #e0e0e0;\\n}\\n\\n/* Ajuste para o tema azul */\\nbody.blue .dropdown-content {\\n  background-color: #2a3447;\\n  border: 2px solid #3a4b6e;\\n}\\n\\n/* Ajuste para o tema verde */\\nbody.green .dropdown-content {\\n  background-color: #2a472a;\\n  border: 2px solid #3a6e3a;\\n}\\n\\n/* Ajuste para o tema roxo */\\nbody.purple .dropdown-content {\\n  background-color: #472a47;\\n  border: 2px solid #6e3a6e;\\n}\\n\\n.scroll-indicator {\\n  position: absolute;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 0;\\n  height: 0;\\n  border-left: 10px solid transparent;\\n  border-right: 10px solid transparent;\\n  opacity: 0.9;\\n  pointer-events: none;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.scroll-indicator.top {\\n  top: 0;\\n  border-bottom: 10px solid #666;\\n}\\n\\n.scroll-indicator.bottom {\\n  bottom: 0;\\n  border-top: 10px solid #666;\\n}\\n\\n/* Ajustes para os temas */\\nbody.dark .scroll-indicator.top {\\n  border-bottom-color: #666;\\n}\\n\\nbody.dark .scroll-indicator.bottom {\\n  border-top-color: #666;\\n}\\n\\nbody.light .scroll-indicator.top {\\n  border-bottom-color: #333;\\n}\\n\\nbody.light .scroll-indicator.bottom {\\n  border-top-color: #333;\\n}\\n\\nbody.blue .scroll-indicator.top {\\n  border-bottom-color: #4a5b8e;\\n}\\n\\nbody.blue .scroll-indicator.bottom {\\n  border-top-color: #4a5b8e;\\n}\\n\\nbody.green .scroll-indicator.top {\\n  border-bottom-color: #4a8e4a;\\n}\\n\\nbody.green .scroll-indicator.bottom {\\n  border-top-color: #4a8e4a;\\n}\\n\\nbody.purple .scroll-indicator.top {\\n  border-bottom-color: #8e4a8e;\\n}\\n\\nbody.purple .scroll-indicator.bottom {\\n  border-top-color: #8e4a8e;\\n}\\n\\n.dropdown-item {\\n  background-color: transparent;\\n  color: #fff;\\n  padding: 10px 16px;\\n  border: none;\\n  text-decoration: none;\\n  display: block;\\n  cursor: pointer;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  text-align: center;\\n  margin: 4px 0;\\n  font-weight: 500;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.dropdown-item:hover {\\n  background-color: #3a3a3a;\\n  transform: translateY(-2px);\\n}\\n\\n.controls {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  flex: 1;\\n  overflow-y: hidden;\\n  overflow-x: hidden;\\n  width: 100%;\\n}\\n\\n#customizationMenu {\\n  display: none;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 1rem;\\n  transition: opacity 0.3s ease;\\n  opacity: 0;\\n  background: #2a2a2a;\\n  padding: 2rem;\\n  border-radius: 15px;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\\n  border: 2px solid #3a3a3a;\\n  width: 300px;\\n}\\n\\n#customizationMenu.visible {\\n  opacity: 1;\\n}\\n\\n#customizationMenu label {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-size: 1.1rem;\\n  color: #e0e0e0;\\n  font-weight: 500;\\n}\\n\\n#customizationMenu select {\\n  width: 150px;\\n  padding: 10px;\\n  border-radius: 8px;\\n  background: #3a3a3a;\\n  color: white;\\n  border: 2px solid #4a4a4a;\\n  margin-bottom: 1rem;\\n  text-align: center;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n#customizationMenu select:hover {\\n  background: #4a4a4a;\\n}\\n\\n#customizationMenu select:focus {\\n  outline: none;\\n  border-color: #5a5a5a;\\n  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);\\n}\\n\\n#scaleRange {\\n  width: 200px;\\n  height: 8px;\\n  -webkit-appearance: none;\\n  appearance: none;\\n  background: #3a3a3a;\\n  outline: none;\\n  border-radius: 4px;\\n  margin: 10px auto;\\n  display: block;\\n}\\n\\n#scaleRange::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 20px;\\n  height: 20px;\\n  background: #fff;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n\\n#scaleRange::-moz-range-thumb {\\n  width: 20px;\\n  height: 20px;\\n  background: #fff;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n\\n#scaleRange::-webkit-slider-thumb:hover {\\n  background: #e0e0e0;\\n  transform: scale(1.1);\\n}\\n\\n#scaleRange::-moz-range-thumb:hover {\\n  background: #e0e0e0;\\n  transform: scale(1.1);\\n}\\n\\n.scale-value {\\n  text-align: center;\\n  margin-top: 8px;\\n  font-size: 0.9rem;\\n  color: #e0e0e0;\\n  font-weight: 500;\\n}\\n\\n/* Ajustes responsivos para diferentes escalas */\\n@media (max-width: 1400px) {\\n  main {\\n    padding: 1rem;\\n  }\\n  \\n  .character-grid {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n\\n@media (max-width: 1200px) {\\n  main {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  \\n  header {\\n    position: relative;\\n    top: 0;\\n    width: 100%;\\n    max-width: 600px;\\n    height: auto;\\n    margin-bottom: 1rem;\\n  }\\n  \\n  .character-grid {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .character-grid {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  \\n  #chosenDisplay {\\n    max-width: 250px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .character-grid {\\n    grid-template-columns: repeat(1, 1fr);\\n  }\\n  \\n  header {\\n    padding: 1rem;\\n  }\\n}\\n\\n#categoryButton {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background-color: #444;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  padding: 12px 24px;\\n  font-size: 16px;\\n  cursor: pointer;\\n  width: calc(100% - 2px);\\n  position: relative;\\n}\\n\\n.button-content {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.button-content span:first-child {\\n  flex: 1;\\n  text-align: center;\\n  white-space: nowrap;\\n  padding-right: 10px;\\n}\\n\\n.arrow {\\n  width: 0;\\n  height: 0;\\n  border-left: 6px solid transparent;\\n  border-right: 6px solid transparent;\\n  border-top: 6px solid white;\\n  transition: transform 0.3s ease;\\n  margin-left: 10px;\\n  flex-shrink: 0;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n#categoryButton:hover .arrow {\\n  transform: rotate(180deg);\\n}\\n\\n/* Ajustes para os temas */\\nbody.light #categoryButton {\\n  background-color: #e0e0e0;\\n  color: #333;\\n}\\n\\nbody.light .arrow {\\n  border-top-color: #333;\\n}\\n\\nbody.blue #categoryButton {\\n  background-color: #3a4b6e;\\n}\\n\\nbody.green #categoryButton {\\n  background-color: #3a6e3a;\\n}\\n\\nbody.purple #categoryButton {\\n  background-color: #6e3a6e;\\n}\\n\\n.character-grid p {\\n  text-align: center;\\n  margin: 20px 0;\\n  min-height: 24px;\\n  white-space: nowrap;\\n}\\n\\n/* Tema Azul */\\nbody.blue {\\n    background: #1a1f2e;\\n    color: #e0e7ff;\\n}\\n\\nbody.blue .header {\\n    background: #2a3447;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.blue .menu-button {\\n    background-color: #3a4b6e;\\n    color: #e0e7ff;\\n    border: 2px solid #4a5b8e;\\n}\\n\\nbody.blue .menu-button::after {\\n    background: rgba(224, 231, 255, 0.2);\\n}\\n\\nbody.blue .menu-button:hover {\\n    background: #4a5b8e;\\n}\\n\\nbody.blue .character {\\n    background: #2a3447;\\n    border: 1px solid #3a4b6e;\\n}\\n\\nbody.blue .character:hover {\\n    border-color: #4a5b8e;\\n    box-shadow: 0 4px 15px rgba(74, 91, 142, 0.3);\\n}\\n\\nbody.blue .customization-menu {\\n    background: #2a3447;\\n    border: 1px solid #3a4b6e;\\n}\\n\\nbody.blue select {\\n    background: #3a4b6e;\\n    color: #e0e7ff;\\n}\\n\\nbody.blue select:hover {\\n    background: #4a5b8e;\\n}\\n\\n/* Tema Verde */\\nbody.green {\\n    background: #1a2e1a;\\n    color: #e0ffe0;\\n}\\n\\nbody.green .header {\\n    background: #2a472a;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.green .menu-button {\\n    background-color: #3a6e3a;\\n    color: #e0ffe0;\\n    border: 2px solid #4a8e4a;\\n}\\n\\nbody.green .menu-button::after {\\n    background: rgba(224, 255, 224, 0.2);\\n}\\n\\nbody.green .menu-button:hover {\\n    background: #4a8e4a;\\n}\\n\\nbody.green .character {\\n    background: #2a472a;\\n    border: 1px solid #3a6e3a;\\n}\\n\\nbody.green .character:hover {\\n    border-color: #4a8e4a;\\n    box-shadow: 0 4px 15px rgba(74, 142, 74, 0.3);\\n}\\n\\nbody.green .customization-menu {\\n    background: #2a472a;\\n    border: 1px solid #3a6e3a;\\n}\\n\\nbody.green select {\\n    background: #3a6e3a;\\n    color: #e0ffe0;\\n}\\n\\nbody.green select:hover {\\n    background: #4a8e4a;\\n}\\n\\n/* Tema Roxo */\\nbody.purple {\\n    background: #2e1a2e;\\n    color: #ffe0ff;\\n}\\n\\nbody.purple .header {\\n    background: #472a47;\\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\\n}\\n\\nbody.purple .menu-button {\\n    background-color: #6e3a6e;\\n    color: #ffe0ff;\\n    border: 2px solid #8e4a8e;\\n}\\n\\nbody.purple .menu-button::after {\\n    background: rgba(255, 224, 255, 0.2);\\n}\\n\\nbody.purple .menu-button:hover {\\n    background: #8e4a8e;\\n}\\n\\nbody.purple .character {\\n    background: #472a47;\\n    border: 1px solid #6e3a6e;\\n}\\n\\nbody.purple .character:hover {\\n    border-color: #8e4a8e;\\n    box-shadow: 0 4px 15px rgba(142, 74, 142, 0.3);\\n}\\n\\nbody.purple .customization-menu {\\n    background: #472a47;\\n    border: 1px solid #6e3a6e;\\n}\\n\\nbody.purple select {\\n    background: #6e3a6e;\\n    color: #ffe0ff;\\n}\\n\\nbody.purple select:hover {\\n    background: #8e4a8e;\\n}\\n\\n/* Ajustes comuns para todos os temas */\\nbody.blue .point-counter,\\nbody.green .point-counter,\\nbody.purple .point-counter {\\n    color: #fff;\\n}\\n\\nbody.blue .character img,\\nbody.green .character img,\\nbody.purple .character img {\\n    border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\nbody.blue .character-name,\\nbody.green .character-name,\\nbody.purple .character-name {\\n    color: #fff;\\n}\\n\\nbody.blue .character-category,\\nbody.green .character-category,\\nbody.purple .character-category {\\n    color: rgba(255, 255, 255, 0.7);\\n}\\n\\n/* Ajustes do slider para os novos temas */\\nbody.blue #scaleRange::-webkit-slider-thumb {\\n    background: #4a5b8e;\\n}\\n\\nbody.green #scaleRange::-webkit-slider-thumb {\\n    background: #4a8e4a;\\n}\\n\\nbody.purple #scaleRange::-webkit-slider-thumb {\\n    background: #8e4a8e;\\n}\\n\\nbody.blue #scaleRange::-moz-range-thumb {\\n    background: #4a5b8e;\\n}\\n\\nbody.green #scaleRange::-moz-range-thumb {\\n    background: #4a8e4a;\\n}\\n\\nbody.purple #scaleRange::-moz-range-thumb {\\n    background: #8e4a8e;\\n}\\n\\n/* Ajuste responsivo para telas menores */\\n@media (max-width: 1200px) {\\n  .character-grid {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n\\n#chosenDisplay {\\n  margin-top: 0.5rem;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.3rem;\\n  transition: opacity 0.3s ease;\\n  opacity: 1;\\n  max-width: 180px;\\n  margin: 0 auto;\\n  position:sticky;\\n}\\n\\n#chosenDisplay.hidden {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n#startMenu {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 1rem;\\n  transition: opacity 0.5s ease;\\n  opacity: 1;\\n}\\n\\n#startMenu.hidden {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n#startMenu.centered-menu {\\n  background: transparent !important;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/styles.css\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js ***!
  \************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/ // css base code, injected by the css-loader\n// eslint-disable-next-line func-names\n\nmodule.exports = function(useSourceMap) {\n    var list = [] // return the list of modules as css string\n    ;\n    list.toString = function toString() {\n        return this.map(function(item) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            var content = cssWithMappingToString(item, useSourceMap);\n            if (item[2]) {\n                return \"@media \".concat(item[2], \" {\").concat(content, \"}\");\n            }\n            return content;\n        }).join(\"\");\n    } // import a list of modules into the list\n    ;\n    // eslint-disable-next-line func-names\n    // @ts-expect-error TODO: fix type\n    list.i = function(modules, mediaQuery, dedupe) {\n        if (typeof modules === \"string\") {\n            // eslint-disable-next-line no-param-reassign\n            modules = [\n                [\n                    null,\n                    modules,\n                    \"\"\n                ]\n            ];\n        }\n        var alreadyImportedModules = {};\n        if (dedupe) {\n            for(var i = 0; i < this.length; i++){\n                // eslint-disable-next-line prefer-destructuring\n                var id = this[i][0];\n                if (id != null) {\n                    alreadyImportedModules[id] = true;\n                }\n            }\n        }\n        for(var _i = 0; _i < modules.length; _i++){\n            var item = [].concat(modules[_i]);\n            if (dedupe && alreadyImportedModules[item[0]]) {\n                continue;\n            }\n            if (mediaQuery) {\n                if (!item[2]) {\n                    item[2] = mediaQuery;\n                } else {\n                    item[2] = \"\".concat(mediaQuery, \" and \").concat(item[2]);\n                }\n            }\n            list.push(item);\n        }\n    };\n    return list;\n};\nfunction cssWithMappingToString(item, useSourceMap) {\n    var content = item[1] || \"\" // eslint-disable-next-line prefer-destructuring\n    ;\n    var cssMapping = item[3];\n    if (!cssMapping) {\n        return content;\n    }\n    if (useSourceMap && typeof btoa === \"function\") {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        var sourceMapping = toComment(cssMapping);\n        var sourceURLs = cssMapping.sources.map(function(source) {\n            return \"/*# sourceURL=\".concat(cssMapping.sourceRoot || \"\").concat(source, \" */\");\n        });\n        return [\n            content\n        ].concat(sourceURLs).concat([\n            sourceMapping\n        ]).join(\"\\n\");\n    }\n    return [\n        content\n    ].join(\"\\n\");\n} // Adapted from convert-source-map (MIT)\nfunction toComment(sourceMap) {\n    // eslint-disable-next-line no-undef\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    return \"/*# \".concat(data, \" */\");\n}\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app! ***!
  \*******************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_app\",\n      function () {\n        return __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_app\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfYXBwJnBhZ2U9JTJGX2FwcCEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxnREFBeUI7QUFDaEQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzY3ODIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9fYXBwXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL19hcHBcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!\n"));

/***/ }),

/***/ "./styles/styles.css":
/*!***************************!*\
  !*** ./styles/styles.css ***!
  \***************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/styles.css\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/styles.css\",\n      function () {\n        content = __webpack_require__(/*! !!../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./styles/styles.css\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/styles.css\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js ***!
  \************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nconst isOldIE = function isOldIE() {\n    let memo;\n    return function memorize() {\n        if (typeof memo === \"undefined\") {\n            // Test for IE <= 9 as proposed by Browserhacks\n            // @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\n            // Tests for existence of standard globals is to allow style-loader\n            // to operate correctly into non-standard environments\n            // @see https://github.com/webpack-contrib/style-loader/issues/177\n            memo = Boolean(window && document && document.all && !window.atob);\n        }\n        return memo;\n    };\n}();\nconst getTargetElement = function() {\n    const memo = {};\n    return function memorize(target) {\n        if (typeof memo[target] === \"undefined\") {\n            let styleTarget = document.querySelector(target);\n            // Special case to return head of iframe instead of iframe itself\n            if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n                try {\n                    // This will throw an exception if access to iframe is blocked\n                    // due to cross-origin restrictions\n                    styleTarget = styleTarget.contentDocument.head;\n                } catch (e) {\n                    // istanbul ignore next\n                    styleTarget = null;\n                }\n            }\n            memo[target] = styleTarget;\n        }\n        return memo[target];\n    };\n}();\nconst stylesInDom = [];\nfunction getIndexByIdentifier(identifier) {\n    let result = -1;\n    for(let i = 0; i < stylesInDom.length; i++){\n        if (stylesInDom[i].identifier === identifier) {\n            result = i;\n            break;\n        }\n    }\n    return result;\n}\nfunction modulesToDom(list, options) {\n    const idCountMap = {};\n    const identifiers = [];\n    for(let i = 0; i < list.length; i++){\n        const item = list[i];\n        const id = options.base ? item[0] + options.base : item[0];\n        const count = idCountMap[id] || 0;\n        const identifier = id + \" \" + count.toString();\n        idCountMap[id] = count + 1;\n        const index = getIndexByIdentifier(identifier);\n        const obj = {\n            css: item[1],\n            media: item[2],\n            sourceMap: item[3]\n        };\n        if (index !== -1) {\n            stylesInDom[index].references++;\n            stylesInDom[index].updater(obj);\n        } else {\n            stylesInDom.push({\n                identifier: identifier,\n                // eslint-disable-next-line @typescript-eslint/no-use-before-define\n                updater: addStyle(obj, options),\n                references: 1\n            });\n        }\n        identifiers.push(identifier);\n    }\n    return identifiers;\n}\nfunction insertStyleElement(options) {\n    const style = document.createElement(\"style\");\n    const attributes = options.attributes || {};\n    if (typeof attributes.nonce === \"undefined\") {\n        const nonce = // eslint-disable-next-line no-undef\n         true ? __webpack_require__.nc : 0;\n        if (nonce) {\n            attributes.nonce = nonce;\n        }\n    }\n    Object.keys(attributes).forEach(function(key) {\n        style.setAttribute(key, attributes[key]);\n    });\n    if (typeof options.insert === \"function\") {\n        options.insert(style);\n    } else {\n        const target = getTargetElement(options.insert || \"head\");\n        if (!target) {\n            throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n        }\n        target.appendChild(style);\n    }\n    return style;\n}\nfunction removeStyleElement(style) {\n    // istanbul ignore if\n    if (style.parentNode === null) {\n        return false;\n    }\n    style.parentNode.removeChild(style);\n}\n/* istanbul ignore next  */ const replaceText = function replaceText() {\n    const textStore = [];\n    return function replace(index, replacement) {\n        textStore[index] = replacement;\n        return textStore.filter(Boolean).join(\"\\n\");\n    };\n}();\nfunction applyToSingletonTag(style, index, remove, obj) {\n    const css = remove ? \"\" : obj.media ? \"@media \" + obj.media + \" {\" + obj.css + \"}\" : obj.css;\n    // For old IE\n    /* istanbul ignore if  */ if (style.styleSheet) {\n        style.styleSheet.cssText = replaceText(index, css);\n    } else {\n        const cssNode = document.createTextNode(css);\n        const childNodes = style.childNodes;\n        if (childNodes[index]) {\n            style.removeChild(childNodes[index]);\n        }\n        if (childNodes.length) {\n            style.insertBefore(cssNode, childNodes[index]);\n        } else {\n            style.appendChild(cssNode);\n        }\n    }\n}\nfunction applyToTag(style, _options, obj) {\n    let css = obj.css;\n    const media = obj.media;\n    const sourceMap = obj.sourceMap;\n    if (media) {\n        style.setAttribute(\"media\", media);\n    } else {\n        style.removeAttribute(\"media\");\n    }\n    if (sourceMap && typeof btoa !== \"undefined\") {\n        css += \"\\n/*# sourceMappingURL=data:application/json;base64,\" + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + \" */\";\n    }\n    // For old IE\n    /* istanbul ignore if  */ if (style.styleSheet) {\n        style.styleSheet.cssText = css;\n    } else {\n        while(style.firstChild){\n            style.removeChild(style.firstChild);\n        }\n        style.appendChild(document.createTextNode(css));\n    }\n}\nlet singleton = null;\nlet singletonCounter = 0;\nfunction addStyle(obj, options) {\n    let style;\n    let update;\n    let remove;\n    if (options.singleton) {\n        const styleIndex = singletonCounter++;\n        style = singleton || (singleton = insertStyleElement(options));\n        update = applyToSingletonTag.bind(null, style, styleIndex, false);\n        remove = applyToSingletonTag.bind(null, style, styleIndex, true);\n    } else {\n        style = insertStyleElement(options);\n        update = applyToTag.bind(null, style, options);\n        remove = function() {\n            removeStyleElement(style);\n        };\n    }\n    update(obj);\n    return function updateStyle(newObj) {\n        if (newObj) {\n            if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap) {\n                return;\n            }\n            update(obj = newObj);\n        } else {\n            remove();\n        }\n    };\n}\nmodule.exports = function(list, options) {\n    options = options || {};\n    // Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n    // tags it will allow on a page\n    if (!options.singleton && typeof options.singleton !== \"boolean\") {\n        options.singleton = isOldIE();\n    }\n    list = list || [];\n    let lastIdentifiers = modulesToDom(list, options);\n    return function update(newList) {\n        newList = newList || [];\n        if (Object.prototype.toString.call(newList) !== \"[object Array]\") {\n            return;\n        }\n        for(let i = 0; i < lastIdentifiers.length; i++){\n            const identifier = lastIdentifiers[i];\n            const index = getIndexByIdentifier(identifier);\n            stylesInDom[index].references--;\n        }\n        const newLastIdentifiers = modulesToDom(newList, options);\n        for(let i = 0; i < lastIdentifiers.length; i++){\n            const identifier = lastIdentifiers[i];\n            const index = getIndexByIdentifier(identifier);\n            if (stylesInDom[index].references === 0) {\n                stylesInDom[index].updater();\n                stylesInDom.splice(index, 1);\n            }\n        }\n        lastIdentifiers = newLastIdentifiers;\n    };\n};\n\n//# sourceMappingURL=injectStylesIntoStyleTag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\n"));

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MyApp; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/styles.css */ \"./styles/styles.css\");\n/* harmony import */ var _styles_styles_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_styles_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction MyApp(param) {\n    let { Component, pageProps } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\CaraAcara\\\\pages\\\\_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n_c = MyApp;\nvar _c;\n$RefreshReg$(_c, \"MyApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUVmLFNBQVNBLE1BQU0sS0FBd0I7UUFBeEIsRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUUsR0FBeEI7SUFDNUIscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDO0tBRndCRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy9fYXBwLmpzP2UwYWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zdHlsZXMvc3R5bGVzLmNzcyc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNeUFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH0pIHtcclxuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPjtcclxufSAiXSwibmFtZXMiOlsiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_app.js\n"));

/***/ }),

/***/ "./node_modules/react/cjs/react-jsx-dev-runtime.development.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV$1 =  jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV$1;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "./node_modules/react/jsx-dev-runtime.js":
/*!***********************************************!*\
  !*** ./node_modules/react/jsx-dev-runtime.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"./node_modules/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx1SkFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcz81Nzc3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5taW4uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!"), __webpack_exec__("./node_modules/next/dist/client/router.js"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);