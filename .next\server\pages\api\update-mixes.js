"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/update-mixes";
exports.ids = ["pages/api/update-mixes"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "@vercel/kv":
/*!*****************************!*\
  !*** external "@vercel/kv" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("@vercel/kv");;

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fupdate-mixes&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cupdate-mixes.js&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fupdate-mixes&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cupdate-mixes.js&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_update_mixes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\update-mixes.js */ \"(api)/./pages/api/update-mixes.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_update_mixes_js__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_update_mixes_js__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_update_mixes_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_update_mixes_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/update-mixes\",\n        pathname: \"/api/update-mixes\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_update_mixes_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fupdate-mixes&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cupdate-mixes.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./data/characterData.js":
/*!*******************************!*\
  !*** ./data/characterData.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   anthropomorphicCharacters: () => (/* binding */ anthropomorphicCharacters),\n/* harmony export */   characters: () => (/* binding */ characters),\n/* harmony export */   nintendoCharacters: () => (/* binding */ nintendoCharacters),\n/* harmony export */   uniqueCharacters: () => (/* binding */ uniqueCharacters)\n/* harmony export */ });\n// data/characterData.js\nconst characters = [\n    {\n        name: \"Birdo\",\n        image: \"/imagens/birdo.jpg\"\n    },\n    {\n        name: \"Dry Bones\",\n        image: \"/imagens/dry.jpg\"\n    },\n    {\n        name: \"Dixie Kong\",\n        image: \"/imagens/dixie.webp\"\n    },\n    {\n        name: \"Pauline\",\n        image: \"/imagens/pauline.jpg\"\n    },\n    {\n        name: \"Rosalina\",\n        image: \"/imagens/rosalina.png\"\n    },\n    {\n        name: \"Kamek\",\n        image: \"/imagens/kamek.png\"\n    },\n    {\n        name: \"Koopa\",\n        image: \"/imagens/koopa.png\"\n    },\n    {\n        name: \"Petey Piranha\",\n        image: \"/imagens/petey.webp\"\n    },\n    {\n        name: \"Goomba\",\n        image: \"/imagens/goomba.jpg\"\n    },\n    {\n        name: \"Toadette\",\n        image: \"/imagens/toadette.png\"\n    },\n    {\n        name: \"James\",\n        image: \"/imagens/james.webp\"\n    },\n    {\n        name: \"Piramid Head\",\n        image: \"/imagens/piramid.webp\"\n    },\n    {\n        name: \"Skull Kid\",\n        image: \"/imagens/skull.jpg\"\n    },\n    {\n        name: \"Krystal\",\n        image: \"/imagens/kristal.webp\"\n    },\n    {\n        name: \"Hammer Bro\",\n        image: \"/imagens/hammer.jpg\"\n    },\n    {\n        name: \"Guile\",\n        image: \"/imagens/guile.jpg\"\n    },\n    {\n        name: \"Ghost Pac-man\",\n        image: \"/imagens/ghost.webp\"\n    },\n    {\n        name: \"Chaim Chomp\",\n        image: \"/imagens/chaim.webp\"\n    },\n    {\n        name: \"Rain World\",\n        image: \"/imagens/rain.jpg\"\n    },\n    {\n        name: \"Niko\",\n        image: \"/imagens/niko.jpg\"\n    },\n    {\n        name: \"Mita\",\n        image: \"/imagens/mita.jpg\"\n    },\n    {\n        name: \"Mineirinho\",\n        image: \"/imagens/mineirinho.jpg\"\n    },\n    {\n        name: \"Little Nightmares\",\n        image: \"/imagens/little.jpg\"\n    },\n    {\n        name: \"Isaac\",\n        image: \"/imagens/isac.jpg\"\n    },\n    {\n        name: \"Among Us\",\n        image: \"/imagens/among.webp\"\n    },\n    {\n        name: \"Leon\",\n        image: \"/imagens/leon.jpg\"\n    },\n    {\n        name: \"Linus\",\n        image: \"/imagens/linus.jpg\"\n    },\n    {\n        name: \"Robin\",\n        image: \"/imagens/robin.jpg\"\n    },\n    {\n        name: \"Sebastian\",\n        image: \"/imagens/sebastian.webp\"\n    },\n    {\n        name: \"Abigail\",\n        image: \"/imagens/abigail.webp\"\n    },\n    {\n        name: \"Lewis\",\n        image: \"/imagens/lewis.webp\"\n    },\n    {\n        name: \"Ratchet\",\n        image: \"/imagens/ratchet.jpg\"\n    },\n    {\n        name: \"Klonoa\",\n        image: \"/imagens/klonoa.jpg\"\n    },\n    {\n        name: \"Meta Knight\",\n        image: \"/imagens/meta.jpg\"\n    },\n    {\n        name: \"Foxy\",\n        image: \"/imagens/foxy.jpg\"\n    },\n    {\n        name: \"Bonnie\",\n        image: \"/imagens/bonnie.webp\"\n    },\n    {\n        name: \"Chica\",\n        image: \"/imagens/chica.webp\"\n    },\n    {\n        name: \"Freddy\",\n        image: \"/imagens/freddy.webp\"\n    },\n    {\n        name: \"Inside\",\n        image: \"/imagens/inside.jpg\"\n    },\n    {\n        name: \"Gris\",\n        image: \"/imagens/gris.png\"\n    },\n    {\n        name: \"Demonio\",\n        image: \"/imagens/demonio.webp\"\n    },\n    {\n        name: \"Caneco\",\n        image: \"/imagens/caneco.webp\"\n    },\n    {\n        name: \"Tiny Tiger\",\n        image: \"/imagens/tinytiger.jpg\"\n    },\n    {\n        name: \"Coco\",\n        image: \"/imagens/coco.webp\"\n    },\n    {\n        name: \"Cortex\",\n        image: \"/imagens/cortex.jpg\"\n    },\n    {\n        name: \"Knight\",\n        image: \"/imagens/knight.jpeg\"\n    },\n    {\n        name: \"Macaco Engenheiro\",\n        image: \"/imagens/macaco.webp\"\n    },\n    {\n        name: \"Mae\",\n        image: \"/imagens/mae.webp\"\n    },\n    {\n        name: \"Blaze\",\n        image: \"/imagens/blaze.webp\"\n    },\n    {\n        name: \"Rouge\",\n        image: \"/imagens/rouge.jpg\"\n    },\n    {\n        name: \"Amy\",\n        image: \"/imagens/amy.webp\"\n    },\n    {\n        name: \"Absol\",\n        image: \"/imagens/absol.webp\"\n    },\n    {\n        name: \"Umbreon\",\n        image: \"/imagens/umbreon.webp\"\n    },\n    {\n        name: \"Espeon\",\n        image: \"/imagens/espeon.webp\"\n    },\n    {\n        name: \"Flareon\",\n        image: \"/imagens/flareon.jpg\"\n    },\n    {\n        name: \"Jolteon\",\n        image: \"/imagens/jolteon.webp\"\n    },\n    {\n        name: \"Vaporeon\",\n        image: \"/imagens/vaporeon.jpeg\"\n    },\n    {\n        name: \"Ditto\",\n        image: \"/imagens/ditto.webp\"\n    },\n    {\n        name: \"Raichu\",\n        image: \"/imagens/raichu.jpg\"\n    },\n    {\n        name: \"Dragonite\",\n        image: \"/imagens/dragonite.jpg\"\n    },\n    {\n        name: \"Blastoise\",\n        image: \"/imagens/blastoise.jpeg\"\n    },\n    {\n        name: \"Snorlax\",\n        image: \"/imagens/snorlax.jpeg\"\n    },\n    {\n        name: \"Gardevoir\",\n        image: \"/imagens/gardevoir.webp\"\n    },\n    {\n        name: \"Garchomp\",\n        image: \"/imagens/garchomp.webp\"\n    },\n    {\n        name: \"Lucario\",\n        image: \"/imagens/lucario.jpg\"\n    },\n    {\n        name: \"Mew\",\n        image: \"/imagens/mew.jpg\"\n    },\n    {\n        name: \"Squirtle\",\n        image: \"/imagens/squirtle.jpeg\"\n    },\n    {\n        name: \"Charmander\",\n        image: \"/imagens/charmander.webp\"\n    },\n    {\n        name: \"Bulbasaur\",\n        image: \"/imagens/bulbasaur.webp\"\n    },\n    {\n        name: \"Eevee\",\n        image: \"/imagens/eevee.jpg\"\n    },\n    {\n        name: \"Charizard\",\n        image: \"/imagens/charizard.jpg\"\n    },\n    {\n        name: \"Tommy Vercetti\",\n        image: \"/imagens/tommy_vercetti.webp\"\n    },\n    {\n        name: \"Shy Guy\",\n        image: \"/imagens/shy_guy.webp\"\n    },\n    {\n        name: \"Papyrus\",\n        image: \"/imagens/papyrus.jpg\"\n    },\n    {\n        name: \"Little Mac\",\n        image: \"/imagens/little_mac.webp\"\n    },\n    {\n        name: \"Liu Kang\",\n        image: \"/imagens/liu_kang.jpg\"\n    },\n    {\n        name: \"King Boo\",\n        image: \"/imagens/king_boo.png\"\n    },\n    {\n        name: \"Knuckles\",\n        image: \"/imagens/knuckles.jpg\"\n    },\n    {\n        name: \"King Dedede\",\n        image: \"/imagens/king_dedede.webp\"\n    },\n    {\n        name: \"Duke Nukem\",\n        image: \"/imagens/duke_nukem.webp\"\n    },\n    {\n        name: \"Diddy Kong\",\n        image: \"/imagens/diddy_kong.png\"\n    },\n    {\n        name: \"Cranky Kong\",\n        image: \"/imagens/cranky_kong.png\"\n    },\n    {\n        name: \"Conker\",\n        image: \"/imagens/conker.jpg\"\n    },\n    {\n        name: \"Sub Zero\",\n        image: \"/imagens/subzero.jpg\"\n    },\n    {\n        name: \"Scorpion\",\n        image: \"/imagens/scorpion.png\"\n    },\n    {\n        name: \"Captain Toad\",\n        image: \"/imagens/captain_toad.png\"\n    },\n    {\n        name: \"Shantae\",\n        image: \"/imagens/shantae.jpeg\"\n    },\n    {\n        name: \"Geno\",\n        image: \"/imagens/geno.webp\"\n    },\n    {\n        name: \"Funky Kong\",\n        image: \"/imagens/funky_kong.jpeg\"\n    },\n    {\n        name: \"Silver\",\n        image: \"/imagens/silver.webp\"\n    },\n    {\n        name: \"Joker\",\n        image: \"/imagens/joker.jpg\"\n    },\n    {\n        name: \"Piranha Plant\",\n        image: \"/imagens/piranha_plant.webp\"\n    },\n    {\n        name: \"Incineroar\",\n        image: \"/imagens/incineroar.jpeg\"\n    },\n    {\n        name: \"King K. Rool\",\n        image: \"/imagens/king_k_rool.jpg\"\n    },\n    {\n        name: \"Inkling\",\n        image: \"/imagens/inkling.webp\"\n    },\n    {\n        name: \"Cloud\",\n        image: \"/imagens/cloud.webp\"\n    },\n    {\n        name: \"Ken\",\n        image: \"/imagens/ken.jpeg\"\n    },\n    {\n        name: \"Duck Hunt\",\n        image: \"/imagens/duck_hunt.png\"\n    },\n    {\n        name: \"Bowser Jr.\",\n        image: \"/imagens/bowser_jr.jpg\"\n    }\n];\nconst uniqueCharacters = characters.filter((char, index, self)=>index === self.findIndex((c)=>c.name === char.name));\nconst nintendoCharacters = [\n    \"Bowser Jr.\",\n    \"Duck Hunt\",\n    \"Ken\",\n    \"Cloud\",\n    \"Inkling\",\n    \"King K. Rool\",\n    \"Incineroar\",\n    \"Piranha Plant\",\n    \"Joker\",\n    \"Silver\",\n    \"Funky Kong\",\n    \"Geno\",\n    \"Shantae\",\n    \"Captain Toad\",\n    \"Scorpion\",\n    \"Sub Zero\",\n    \"Conker\",\n    \"Cranky Kong\",\n    \"Diddy Kong\",\n    \"Duke Nukem\",\n    \"King Dedede\",\n    \"Knuckles\",\n    \"King Boo\",\n    \"Liu Kang\",\n    \"Little Mac\",\n    \"Papyrus\",\n    \"Shy Guy\",\n    \"Tommy Vercetti\",\n    \"Charizard\",\n    \"Eevee\",\n    \"Bulbasaur\",\n    \"Charmander\",\n    \"Squirtle\",\n    \"Mew\",\n    \"Lucario\",\n    \"Garchomp\",\n    \"Gardevoir\",\n    \"Snorlax\",\n    \"Blastoise\",\n    \"Dragonite\",\n    \"Raichu\",\n    \"Ditto\",\n    \"Vaporeon\",\n    \"Jolteon\",\n    \"Flareon\",\n    \"Espeon\",\n    \"Umbreon\",\n    \"Absol\",\n    \"Amy\",\n    \"Rouge\",\n    \"Blaze\",\n    \"Mae\",\n    \"Macaco Engenheiro\",\n    \"Knight\",\n    \"Cortex\",\n    \"Coco\",\n    \"Tiny Tiger\",\n    \"Caneco\",\n    \"Demonio\",\n    \"Gris\",\n    \"Inside\",\n    \"Freddy\",\n    \"Chica\",\n    \"Bonnie\",\n    \"Foxy\",\n    \"Meta Knight\",\n    \"Klonoa\",\n    \"Ratchet\",\n    \"Lewis\",\n    \"Robin\",\n    \"Sebastian\",\n    \"Abigail\",\n    \"Linus\",\n    \"Leon\",\n    \"Among Us\",\n    \"Isaac\",\n    \"Little Nightmares\",\n    \"Mineirinho\",\n    \"Mita\",\n    \"Niko\",\n    \"Rain World\",\n    \"Chaim Chomp\",\n    \"Ghost Pac-man\",\n    \"Guile\",\n    \"Hammer Bro\",\n    \"Krystal\",\n    \"Skull Kid\",\n    \"Piramid Head\",\n    \"James\",\n    \"Toadette\",\n    \"Goomba\",\n    \"Koopa\",\n    \"Petey Piranha\",\n    \"Kamek\",\n    \"Rosalina\",\n    \"Pauline\",\n    \"Dixie Kong\",\n    \"Dry Bones\",\n    \"Birdo\"\n];\nconst anthropomorphicCharacters = [\n    \"Charizard\",\n    \"Eevee\",\n    \"Bulbasaur\",\n    \"Charmander\",\n    \"Squirtle\",\n    \"Mew\",\n    \"Lucario\",\n    \"Garchomp\",\n    \"Gardevoir\",\n    \"Snorlax\",\n    \"Blastoise\",\n    \"Dragonite\",\n    \"Raichu\",\n    \"Ditto\",\n    \"Vaporeon\",\n    \"Jolteon\",\n    \"Flareon\",\n    \"Espeon\",\n    \"Umbreon\",\n    \"Absol\",\n    \"Amy\",\n    \"Rouge\",\n    \"Blaze\",\n    \"Mae\",\n    \"Macaco Engenheiro\",\n    \"Knight\",\n    \"Cortex\",\n    \"Coco\",\n    \"Tiny Tiger\",\n    \"Caneco\",\n    \"Demonio\",\n    \"Gris\",\n    \"Inside\",\n    \"Freddy\",\n    \"Chica\",\n    \"Bonnie\",\n    \"Foxy\",\n    \"Meta Knight\",\n    \"Klonoa\",\n    \"Ratchet\",\n    \"Lewis\",\n    \"Robin\",\n    \"Sebastian\",\n    \"Abigail\",\n    \"Linus\",\n    \"Leon\",\n    \"Among Us\",\n    \"Isaac\",\n    \"Little Nightmares\",\n    \"Mineirinho\",\n    \"Mita\",\n    \"Niko\",\n    \"Rain World\",\n    \"Chaim Chomp\",\n    \"Ghost Pac-man\",\n    \"Guile\",\n    \"Hammer Bro\",\n    \"Krystal\",\n    \"Skull Kid\",\n    \"Piramid Head\",\n    \"James\",\n    \"Toadette\",\n    \"Goomba\",\n    \"Koopa\",\n    \"Petey Piranha\",\n    \"Kamek\",\n    \"Rosalina\",\n    \"Pauline\",\n    \"Dixie Kong\",\n    \"Dry Bones\",\n    \"Birdo\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./data/characterData.js\n");

/***/ }),

/***/ "(api)/./pages/api/update-mixes.js":
/*!***********************************!*\
  !*** ./pages/api/update-mixes.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _vercel_kv__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @vercel/kv */ \"@vercel/kv\");\n/* harmony import */ var _utils_helpers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/helpers.js */ \"(api)/./utils/helpers.js\");\n/* harmony import */ var _data_characterData_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../data/characterData.js */ \"(api)/./data/characterData.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_vercel_kv__WEBPACK_IMPORTED_MODULE_0__]);\n_vercel_kv__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // Verifica se os mixes precisam ser atualizados\n        const lastUpdate = await _vercel_kv__WEBPACK_IMPORTED_MODULE_0__.kv.get(\"last_mix_update\");\n        const now = new Date();\n        // Se não houver timestamp ou se for um novo dia à meia-noite\n        if (!lastUpdate || now.getHours() === 0 && now.getMinutes() === 0 && now.getDate() !== new Date(lastUpdate).getDate()) {\n            console.log(\"--- Iniciando atualiza\\xe7\\xe3o dos Mixes ---\");\n            // Verifica se o KV está inicializado\n            if (!_vercel_kv__WEBPACK_IMPORTED_MODULE_0__.kv) {\n                throw new Error(\"KV store n\\xe3o est\\xe1 inicializado\");\n            }\n            const numCharsPerMix = 50;\n            // Verifica se temos personagens suficientes\n            if (!_data_characterData_js__WEBPACK_IMPORTED_MODULE_2__.uniqueCharacters || _data_characterData_js__WEBPACK_IMPORTED_MODULE_2__.uniqueCharacters.length < numCharsPerMix) {\n                throw new Error(`Não há personagens suficientes. Necessário: ${numCharsPerMix}, Disponível: ${_data_characterData_js__WEBPACK_IMPORTED_MODULE_2__.uniqueCharacters?.length || 0}`);\n            }\n            console.log(`Gerando ${numCharsPerMix} personagens para cada mix...`);\n            // Gera os mixes\n            const mix1Chars = (0,_utils_helpers_js__WEBPACK_IMPORTED_MODULE_1__.getRandomCharacters)(_data_characterData_js__WEBPACK_IMPORTED_MODULE_2__.uniqueCharacters, numCharsPerMix);\n            const mix2Chars = (0,_utils_helpers_js__WEBPACK_IMPORTED_MODULE_1__.getRandomCharacters)(_data_characterData_js__WEBPACK_IMPORTED_MODULE_2__.uniqueCharacters, numCharsPerMix);\n            const mix3Chars = (0,_utils_helpers_js__WEBPACK_IMPORTED_MODULE_1__.getRandomCharacters)(_data_characterData_js__WEBPACK_IMPORTED_MODULE_2__.uniqueCharacters, numCharsPerMix);\n            // Prepara os dados para salvar\n            const mix1Data = mix1Chars.map((char)=>({\n                    name: char.name,\n                    image: char.image\n                }));\n            const mix2Data = mix2Chars.map((char)=>({\n                    name: char.name,\n                    image: char.image\n                }));\n            const mix3Data = mix3Chars.map((char)=>({\n                    name: char.name,\n                    image: char.image\n                }));\n            // Salva os mixes no KV\n            await _vercel_kv__WEBPACK_IMPORTED_MODULE_0__.kv.set(\"mix1_characters\", mix1Data);\n            await _vercel_kv__WEBPACK_IMPORTED_MODULE_0__.kv.set(\"mix2_characters\", mix2Data);\n            await _vercel_kv__WEBPACK_IMPORTED_MODULE_0__.kv.set(\"mix3_characters\", mix3Data);\n            // Atualiza o timestamp\n            const currentTime = Date.now();\n            await _vercel_kv__WEBPACK_IMPORTED_MODULE_0__.kv.set(\"last_mix_update\", currentTime);\n            console.log(\"Mixes atualizados com sucesso!\");\n            return res.status(200).json({\n                message: \"Mixes atualizados com sucesso\",\n                timestamp: currentTime\n            });\n        }\n        return res.status(200).json({\n            message: \"Mixes j\\xe1 est\\xe3o atualizados\",\n            timestamp: lastUpdate\n        });\n    } catch (error) {\n        console.error(\"Erro ao atualizar mixes:\", error);\n        return res.status(500).json({\n            message: \"Erro ao atualizar mixes\",\n            error: error.message\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/update-mixes.js\n");

/***/ }),

/***/ "(api)/./utils/helpers.js":
/*!**************************!*\
  !*** ./utils/helpers.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRandomCharacters: () => (/* binding */ getRandomCharacters),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray)\n/* harmony export */ });\n// utils/helpers.js\nfunction shuffleArray(array) {\n    const newArray = [\n        ...array\n    ];\n    for(let i = newArray.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [newArray[i], newArray[j]] = [\n            newArray[j],\n            newArray[i]\n        ];\n    }\n    return newArray;\n}\nfunction getRandomCharacters(sourceArray, count) {\n    if (!Array.isArray(sourceArray) || sourceArray.length === 0) {\n        console.warn(\"Array de origem inv\\xe1lido ou vazio\");\n        return [];\n    }\n    const numToPick = Math.min(count, sourceArray.length);\n    return shuffleArray(sourceArray).slice(0, numToPick);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./utils/helpers.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fupdate-mixes&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cupdate-mixes.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();